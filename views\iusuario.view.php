<?php #region docs
/** @var Perfil[] $perfiles */
/** @var Empleado[] $empleados */

use App\classes\Perfil;
use App\classes\Empleado;

#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Crear Usuario</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
    <style>
        /* Style for invalid fields */
        .is-invalid {
            border-color: #dc3545 !important; /* Bootstrap's default danger color */
        }
        /* Style for validation messages */
        .invalid-feedback {
            display: none; /* Hide by default */
            width: 100%;
            margin-top: .25rem;
            font-size: .875em; /* 14px if base is 16px */
            color: #dc3545;
        }
        .is-invalid ~ .invalid-feedback {
            display: block; /* Show when input is invalid */
        }
        /* Optional: Style username field visually as uppercase */
        #username {
            text-transform: uppercase;
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Crear Nuevo Usuario</h4>
                <p class="mb-0 text-muted">Ingrese los detalles del nuevo usuario. El usuario se creará como activo.</p>
            </div>
            <div class="ms-auto">
                <a href="lusuarios" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver a la Lista</a>
            </div>
        </div>
		<hr>
		<?php #endregion PAGE HEADER ?>

        <?php /* MESSAGES region removed as requested */ ?>

		<?php #region region FORM ?>
		<form action="iusuario" method="POST" id="create-user-form" novalidate> <?php /* novalidate prevents default browser validation */ ?>
            <?php #region region PANEL USER DETAILS ?>
            <div class="panel panel-inverse no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">Detalles del Usuario</h4>
                    <div class="panel-heading-btn">
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                    </div>
                </div>
                <div class="panel-body">

                    <!-- Fields based on Usuario class: nombre, username, clave -->
                    <!-- Estado is handled by the class constructor -->

                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre Completo <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nombre" name="nombre" value="<?php echo htmlspecialchars($nombre ?? ''); ?>" required>
                        <div class="invalid-feedback" id="nombre-error">El nombre completo es requerido.</div>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">Nombre de Usuario <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                        <div class="invalid-feedback" id="username-error">El nombre de usuario es requerido.</div>
                    </div>

                    <div class="mb-3">
                        <label for="id_perfil" class="form-label">Perfil <span class="text-danger">*</span></label>
                        <select class="form-select" id="id_perfil" name="id_perfil" required>
                            <option value="">Seleccione un perfil</option>
                            <?php foreach ($perfiles as $perfil): ?>
                                <option value="<?php echo $perfil->getId(); ?>" <?php echo (isset($id_perfil) && $id_perfil == $perfil->getId()) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($perfil->getNombre()); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback" id="id_perfil-error">Debe seleccionar un perfil.</div>
                    </div>

                    <div class="mb-3">
                        <label for="id_empleado" class="form-label">Barbero (opcional):</label>
                        <select class="form-select" id="id_empleado" name="id_empleado">
                            <option value="">Seleccione un empleado</option>
                            <?php foreach ($empleados as $empleado): ?>
                                <option value="<?php echo $empleado->getId(); ?>" <?php echo (isset($id_empleado) && $id_empleado == $empleado->getId()) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($empleado->getNombre()); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small class="text-muted">Asociar este usuario con un barbero existente.</small>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="clave" class="form-label">Contraseña (Clave) <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="clave" name="clave" required>
                            <div class="invalid-feedback" id="clave-error">La contraseña es requerida.</div>
                        </div>
                        <div class="col-md-6">
                            <label for="confirm_clave" class="form-label">Confirmar Contraseña <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_clave" name="confirm_clave" required>
                            <div class="invalid-feedback" id="confirm_clave-error">Por favor confirme la contraseña.</div>
                        </div>
                    </div>

                    <!-- Estado field removed as requested -->

                </div>
                <div class="panel-footer text-end">
                    <button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw me-1"></i> Guardar Usuario</button>
                    <a href="lusuarios" class="btn btn-secondary"><i class="fa fa-times fa-fw me-1"></i> Cancelar</a>
                </div>
            </div>
            <?php #endregion PANEL USER DETAILS ?>
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<?php #region region CLIENT-SIDE VALIDATION & INPUT HANDLING SCRIPT ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('create-user-form');
    const nombreInput = document.getElementById('nombre');
    const usernameInput = document.getElementById('username');
    const idPerfilSelect = document.getElementById('id_perfil');
    const claveInput = document.getElementById('clave');
    const confirmClaveInput = document.getElementById('confirm_clave');

    // Error message elements
    const nombreError = document.getElementById('nombre-error');
    const usernameError = document.getElementById('username-error');
    const idPerfilError = document.getElementById('id_perfil-error');
    const claveError = document.getElementById('clave-error');
    const confirmClaveError = document.getElementById('confirm_clave-error');

    // --- Force Username to Uppercase ---
    usernameInput.addEventListener('input', function() {
        // Store the current cursor position
        const start = this.selectionStart;
        const end = this.selectionEnd;
        // Convert value to uppercase
        this.value = this.value.toUpperCase();
        // Restore the cursor position
        this.setSelectionRange(start, end);
    });
    // Also apply CSS for visual consistency while typing
    usernameInput.style.textTransform = 'uppercase';
    // --- End Uppercase Handling ---


    form.addEventListener('submit', function(event) {
        let isValid = true;

        // Reset previous validation states
        [nombreInput, usernameInput, idPerfilSelect, claveInput, confirmClaveInput].forEach(input => {
            input.classList.remove('is-invalid');
        });
        [nombreError, usernameError, idPerfilError, claveError, confirmClaveError].forEach(errorEl => {
            errorEl.textContent = ''; // Clear previous messages if needed
        });
        confirmClaveError.textContent = 'Por favor confirme la contraseña.'; // Reset default msg


        // Validate Nombre
        if (nombreInput.value.trim() === '') {
            isValid = false;
            nombreInput.classList.add('is-invalid');
            nombreError.textContent = 'El nombre completo es requerido.';
        }

        // Validate Username
        if (usernameInput.value.trim() === '') { // Validation still uses the uppercase value
            isValid = false;
            usernameInput.classList.add('is-invalid');
            usernameError.textContent = 'El nombre de usuario es requerido.';
        }

        // Validate Perfil
        if (idPerfilSelect.value === '') {
            isValid = false;
            idPerfilSelect.classList.add('is-invalid');
            idPerfilError.textContent = 'Debe seleccionar un perfil.';
        }

        // Validate Clave (Password)
        if (claveInput.value === '') {
            isValid = false;
            claveInput.classList.add('is-invalid');
            claveError.textContent = 'La contraseña es requerida.';
        }

        // Validate Confirm Clave (Confirm Password)
        if (confirmClaveInput.value === '') {
            isValid = false;
            confirmClaveInput.classList.add('is-invalid');
            confirmClaveError.textContent = 'Por favor confirme la contraseña.';
        } else if (claveInput.value !== confirmClaveInput.value) {
            isValid = false;
            confirmClaveInput.classList.add('is-invalid');
            confirmClaveError.textContent = 'Las contraseñas no coinciden.';
        }

        // Prevent form submission if validation fails
        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
            // Optional: focus the first invalid field
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.focus();
            }
        }
    });

    // Optional: Real-time validation feedback as user types (or on blur)
    [nombreInput, usernameInput, idPerfilSelect, claveInput, confirmClaveInput].forEach(input => {
        input.addEventListener('input', () => {
            // Simple check to remove invalid state when user starts typing
            if (input.classList.contains('is-invalid')) {
                input.classList.remove('is-invalid');
            }
            // More complex real-time checks could be added here if desired
        });
    });
});
</script>
<?php #endregion CLIENT-SIDE VALIDATION & INPUT HANDLING SCRIPT ?>
<?php #endregion JS ?>

</body>
</html>