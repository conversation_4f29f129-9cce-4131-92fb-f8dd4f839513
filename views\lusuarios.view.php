<?php
#region region DOCS

/** @var Usuario[] $usuarios */
/** @var Perfil[] $perfiles */
/** @var Empleado[] $empleados */

use App\classes\Usuario;
use App\classes\Perfil;
use App\classes\Empleado;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Usuarios</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Usuarios</h4>
				<p class="mb-0 text-muted">Administra los usuarios del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="iusuario" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo</a>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PANEL USUARIOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Usuarios Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE USUARIOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th>Acciones</th>
						<th>Nombre</th>
						<th>Username</th>
						<th>Perfil</th>
						<th>Estado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="user-table-body">
					<?php foreach ($usuarios as $usuario): ?>
						<tr data-user-id="<?php echo $usuario->getId(); ?>">
							<td>
								<?php // Edit Button - Triggers Modal ?>
								<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-usuario"
								        title="Editar Usuario"
								        data-bs-toggle="modal"
								        data-bs-target="#editUserModal"
								        data-userid="<?php echo $usuario->getId(); ?>"
								        data-nombre="<?php echo htmlspecialchars($usuario->getNombre() ?? ''); ?>"
								        data-id-perfil="<?php echo $usuario->getId_perfil() ?? ''; ?>"
								        data-id-empleado="<?php echo $usuario->getId_empleado() ?? ''; ?>"
								        data-nombre-empleado="<?php echo htmlspecialchars($usuario->getNombre_empleado() ?? ''); ?>">
									<i class="fa fa-edit"></i>
								</button>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($usuario->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-usuario"
									        title="Desactivar"
									        data-userid="<?php echo $usuario->getId(); ?>"
									        data-username="<?php echo htmlspecialchars($usuario->getUsername() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td class="user-nombre-cell"><?php echo htmlspecialchars($usuario->getNombre()); ?></td>
							<td><?php echo htmlspecialchars($usuario->getUsername()); ?></td>
							<td class="user-perfil-cell"><?php echo htmlspecialchars($usuario->getNombre_perfil() ?? 'Sin perfil'); ?></td>
							<td> <?php // Display Status ?>
								<?php if ($usuario->isActivo()): ?>
									<span class="badge bg-success">Activo</span>
								<?php else: ?>
									<span class="badge bg-danger">Inactivo</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($usuarios)): ?>
						<tr>
							<td colspan="5" class="text-center">No hay usuarios para mostrar.</td>
						</tr>
					<?php endif; ?>

					</tbody>
				</table>
				<?php #endregion TABLE USUARIOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL USUARIOS ?>
		<?php #region region Edit User Modal ?>
		<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-user-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editUserModalLabel">Editar Usuario</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="userId" id="edit-user-id">
							<input type="hidden" name="action" value="modificar">

							<div class="mb-3">
								<label for="edit-user-nombre" class="form-label">Nombre:</label>
								<input type="text" class="form-control" id="edit-user-nombre" name="nombre" required>
							</div>

							<div class="mb-3">
								<label for="edit-user-perfil" class="form-label">Perfil:</label>
								<select class="form-select" id="edit-user-perfil" name="id_perfil" required>
									<option value="">Seleccione un perfil</option>
									<?php foreach ($perfiles as $perfil): ?>
										<option value="<?php echo $perfil->getId(); ?>">
											<?php echo htmlspecialchars($perfil->getNombre()); ?>
										</option>
									<?php endforeach; ?>
								</select>
								<div class="invalid-feedback">Debe seleccionar un perfil.</div>
							</div>

							<div class="mb-3">
								<label for="edit-user-empleado" class="form-label">Barbero (opcional):</label>
								<select class="form-select" id="edit-user-empleado" name="id_empleado">
									<option value="">Seleccione un empleado</option>
									<?php foreach ($empleados as $empleado): ?>
										<option value="<?php echo $empleado->getId(); ?>">
											<?php echo htmlspecialchars($empleado->getNombre()); ?>
										</option>
									<?php endforeach; ?>
								</select>
								<small class="text-muted">Asociar este usuario con un empleado existente.</small>
							</div>

							<div id="edit-user-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar Cambios</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit User Modal ?>

	</div>
	<!-- END #content -->

	<?php #region region Hidden Form for Deactivation ?>
	<form id="deactivate-user-form" method="POST" action="lusuarios" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="userId" id="deactivate-user-id">
	</form>
	<?php #endregion Hidden Form ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<!-- Assuming SweetAlert is loaded in core_js or globally -->
<!-- Assuming SweetAlert helper functions (showSweetAlertSuccess/Error) are available -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody            = document.getElementById('user-table-body');
        const editUserModalElement = document.getElementById('editUserModal');
        const editUserModal        = new bootstrap.Modal(editUserModalElement); // Initialize Bootstrap Modal
        const editUserForm         = document.getElementById('edit-user-form');
        const editUserIdInput      = document.getElementById('edit-user-id');
        const editUserNombreInput  = document.getElementById('edit-user-nombre');
        const editUserErrorDiv     = document.getElementById('edit-user-error');

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-usuario');
                const editButton       = event.target.closest('.btn-edit-usuario');

                // --- Handle Deactivate Click ---
				<?php #region region JS AJAX -- Deactivate user ?>
                if (deactivateButton) {
                    event.preventDefault();
                    const userId   = deactivateButton.dataset.userid;
                    const userName = deactivateButton.dataset.username || 'este usuario';

                    swal({
                        title     : "Confirmar Desactivación",
                        text      : `¿Seguro que quieres desactivar al usuario '${userName}'?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                document.getElementById('deactivate-user-id').value = userId;
                                document.getElementById('deactivate-user-form').submit();
                            }
                        });
                }
				<?php #endregion JS AJAX -- Deactivate user ?>

                // --- Handle Edit Click ---
				<?php #region region JS AJAX - Handle Edit click ?>
                if (editButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const userId        = editButton.dataset.userid;
                    const currentNombre = editButton.dataset.nombre;
                    const currentPerfil = editButton.dataset.idPerfil;
                    const currentEmpleado = editButton.dataset.idEmpleado;
                    const currentEmpleadoNombre = editButton.dataset.nombreEmpleado;

                    // Populate the modal form
                    editUserIdInput.value          = userId;
                    editUserNombreInput.value      = currentNombre;
                    editUserErrorDiv.style.display = 'none'; // Hide previous errors
                    editUserErrorDiv.textContent   = '';

                    // Set the selected perfil
                    const editUserPerfilSelect = document.getElementById('edit-user-perfil');
                    if (editUserPerfilSelect) {
                        editUserPerfilSelect.value = currentPerfil;
                    }

                    // Set the empleado ID if available
                    const editUserEmpleadoSelect = document.getElementById('edit-user-empleado');
                    if (editUserEmpleadoSelect) {
                        editUserEmpleadoSelect.value = currentEmpleado || '';

                        // If no matching option is found but we have an ID, create a new option
                        if (currentEmpleado && !Array.from(editUserEmpleadoSelect.options).some(option => option.value === currentEmpleado)) {
                            const newOption = new Option(currentEmpleadoNombre || `Empleado ID: ${currentEmpleado}`, currentEmpleado);
                            editUserEmpleadoSelect.add(newOption);
                            editUserEmpleadoSelect.value = currentEmpleado;
                        }
                    }

                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                    // If they weren't there, you'd call: editUserModal.show();
                }
				<?php #endregion JS AJAX - Edit user ?>
            });
        }

        // --- Handle Edit Form Submission (AJAX) ---
		<?php #region region JS AJAX - Edit Form Submission ?>
        if (editUserForm) {
            editUserForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                editUserErrorDiv.style.display = 'none'; // Hide error div initially

                const formData  = new FormData(editUserForm);
                const userId    = formData.get('userId');
                const newNombre = formData.get('nombre').trim(); // Get trimmed name
                const idPerfil  = formData.get('id_perfil');
                let idEmpleado = formData.get('id_empleado');

                // Handle empty employee selection
                if (idEmpleado === '') {
                    idEmpleado = null;
                }

                // Basic client-side validation
                let isValid = true;
                let errorMessage = '';

                if (!newNombre) {
                    isValid = false;
                    errorMessage = 'El nombre no puede estar vacío.';
                }

                if (!idPerfil) {
                    isValid = false;
                    errorMessage = errorMessage ? errorMessage + ' El perfil es requerido.' : 'El perfil es requerido.';
                }

                if (!isValid) {
                    editUserErrorDiv.textContent = errorMessage;
                    editUserErrorDiv.style.display = 'block';
                    return; // Stop submission
                }


                // Disable submit button during request? (Optional)
                const submitButton    = editUserForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;


                fetch('lusuarios', { // Post to the same controller page
                    method: 'POST',
                    body  : formData // FormData handles content type automatically
                })
                    .then(response => {
                        // Check if response is ok (status 200-299) AND is JSON
                        if (!response.ok) {
                            // Try to parse error from JSON, otherwise use status text
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                // If response wasn't JSON or parsing failed
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json(); // Parse JSON body
                    })
                    .then(data => {
                        if (data.success) {
                            editUserModal.hide(); // Close modal on success

                            // Update the row in the table directly
                            const tableRow = document.querySelector(`#user-table-body tr[data-user-id="${userId}"]`);
                            if (tableRow) {
                                // Find the cells with the specific classes within that row
                                const nombreCell = tableRow.querySelector('.user-nombre-cell');
                                if (nombreCell) {
                                    nombreCell.textContent = newNombre; // Update cell text
                                }

                                // Update the perfil cell if it exists in the response
                                if (data.nombre_perfil) {
                                    const perfilCell = tableRow.querySelector('.user-perfil-cell');
                                    if (perfilCell) {
                                        perfilCell.textContent = data.nombre_perfil;
                                    }
                                }

                                // Also update the data attributes on the edit button for next time
                                const editButton = tableRow.querySelector('.btn-edit-usuario');
                                if (editButton) {
                                    editButton.dataset.nombre = newNombre;
                                    editButton.dataset.idPerfil = idPerfil;
                                }
                            }

                            showSweetAlertSuccess('Éxito', 'Nombre actualizado correctamente.');

                        } else {
                            // Show error message inside the modal
                            editUserErrorDiv.textContent   = data.message || 'Ocurrió un error al guardar.';
                            editUserErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error updating user name:', error);
                        // Show error message inside the modal
                        editUserErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        editUserErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
		<?php #endregion JS AJAX - Edit Form Submission ?>
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>